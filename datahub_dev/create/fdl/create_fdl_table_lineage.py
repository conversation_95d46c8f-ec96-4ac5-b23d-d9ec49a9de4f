"""
# @Time    : 2025/6/7 20:25
# <AUTHOR> <PERSON><PERSON><PERSON>.Li
# @File    : create_fdl_table_lineage.py
# @Desc    : 
"""
from utils_new.datahub_client import create_quick_incremental_lineage
from utils_new.pg_client import Database

db = Database()


if __name__ == "__main__":
    # 查询FDL任务之间的关系
    results = db.execute_sql("""
                             SELECT DISTINCT task_id                                        AS up
                                           , 'FineDataLink'                                 AS up_type
                                           , CASE
                                                 WHEN table_name LIKE '%.%' THEN table_name
                                                 WHEN data_schema IS NOT NULL THEN CONCAT(data_schema, '.', table_name)
                                                 ELSE CONCAT('finer3_crm.', table_name) END AS down
                                           , 'mysql'                                        AS down_type
                                           , table_tag
                             FROM test_fdl_it_task_table
                             WHERE data_source IN ('postgresql', 'mysql', 'swift')
                               AND conn_name IN ('CRM')
                               AND table_tag LIKE '数据表输出'
                             UNION ALL
                             SELECT DISTINCT CASE
                                                 WHEN table_name LIKE '%.%' THEN table_name
                                                 WHEN data_schema IS NOT NULL THEN CONCAT(data_schema, '.', table_name)
                                                 ELSE CONCAT('finer3_crm.', table_name) END AS up
                                           , 'mysql'                                        AS up
                                           , task_id                                        AS down
                                           , 'FineDataLink'                                 AS down_type
                                           , table_tag
                             FROM test_fdl_it_task_table
                             WHERE data_source IN ('postgresql', 'mysql', 'swift')
                               AND conn_name IN ('CRM')
                               AND table_tag LIKE '数据表输入'
                             """)

    if results:
        for row in results:
            upstream_dataset = row[0] if row[0] else "NULL"  # up
            upstream_platform = row[1] if row[1] else "FineDataLink"  # up_type
            downstream_dataset = row[2] if row[2] else "NULL"  # down
            downstream_platform = row[3] if row[3] else "mysql"  # down_type
            table_tag = row[4] if row[4] else ""  # table_tag
            
            result = create_quick_incremental_lineage(
                upstream_datasets=[upstream_dataset],
                downstream_dataset=downstream_dataset,
                upstream_platform=upstream_platform,
                downstream_platform=downstream_platform
            )
            print(f"处理血缘关系: {upstream_dataset}({upstream_platform}) -> {downstream_dataset}({downstream_platform}), 标签: {table_tag}, 结果: {result}")
    else:
        print("未查询到任何数据")
