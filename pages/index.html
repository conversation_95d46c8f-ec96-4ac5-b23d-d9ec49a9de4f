<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据资产地图 - 全域数据资产管理平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div class="main-container">
        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 数据资产分类导航 -->
            <section class="asset-categories">
                <h2 class="section-title">
                    <i class="fas fa-layer-group"></i>
                    数据资产分类
                </h2>
                <div class="categories-nav" id="categories-nav">
                    <!-- 动态生成资产分类导航 -->
                </div>
            </section>

            <!-- 数据域概览 -->
            <section class="domains-overview">
                <h2 class="section-title">
                    <i class="fas fa-sitemap"></i>
                    数据资产域概览
                </h2>
                <div class="domains-grid" id="domains-grid">
                    <!-- 动态生成业务域卡片 -->
                </div>
            </section>

            <!-- 数据血缘关系图 -->
            <section class="data-lineage-section">
                <h2 class="section-title">
                    <i class="fas fa-share-alt"></i>
                    数据血缘关系图
                </h2>
                <div class="lineage-container" id="lineage-container">
                    <svg id="lineage-svg" width="100%" height="400">
                        <!-- 数据血缘关系图将在这里生成 -->
                    </svg>
                </div>
            </section>

            <!-- 热门数据资产 -->
            <section class="popular-assets">
                <h2 class="section-title">
                    <i class="fas fa-fire"></i>
                    热门数据资产
                </h2>
                <div class="assets-grid" id="assets-grid">
                    <!-- 动态生成数据资产卡片 -->
                </div>
            </section>

            <!-- 数据资产质量监控 -->
            <section class="asset-quality">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    数据资产质量监控
                </h2>
                <div class="quality-dashboard">
                    <div class="quality-card">
                        <div class="quality-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="quality-info">
                            <h3>数据完整性</h3>
                            <div class="progress-bar">
                                <div class="progress-fill" data-progress="95"></div>
                            </div>
                            <span class="progress-text">95%</span>
                        </div>
                    </div>
                    <div class="quality-card">
                        <div class="quality-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="quality-info">
                            <h3>数据时效性</h3>
                            <div class="progress-bar">
                                <div class="progress-fill" data-progress="88"></div>
                            </div>
                            <span class="progress-text">88%</span>
                        </div>
                    </div>
                    <div class="quality-card">
                        <div class="quality-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="quality-info">
                            <h3>数据准确性</h3>
                            <div class="progress-bar">
                                <div class="progress-fill" data-progress="92"></div>
                            </div>
                            <span class="progress-text">92%</span>
                        </div>
                    </div>
                    <div class="quality-card">
                        <div class="quality-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="quality-info">
                            <h3>API可用性</h3>
                            <div class="progress-bar">
                                <div class="progress-fill" data-progress="98"></div>
                            </div>
                            <span class="progress-text">98%</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 详情模态框 -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modal-body">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载数据资产地图...</p>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
