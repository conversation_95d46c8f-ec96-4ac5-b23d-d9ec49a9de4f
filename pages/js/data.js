// 数据资产配置数据
const DATA_CONFIG = {
    // 数据平台分类
    platforms: [
        {
            id: 'mysql',
            name: 'MySQL',
            icon: 'fas fa-database',
            description: 'MySQL关系型数据库平台',
            color: '#4285f4',
            count: 7000,
            type: 'database'
        },
        {
            id: 'postgresql',
            name: 'PostgreSQL',
            icon: 'fas fa-server',
            description: 'PostgreSQL高级关系型数据库',
            color: '#336791',
            count: 3400,
            type: 'database'
        },
        {
            id: 'finereport',
            name: 'FineReport',
            icon: 'fas fa-chart-line',
            description: 'FineReport企业级报表平台',
            color: '#fd79a8',
            count: 1400,
            type: 'report'
        },
        {
            id: 'finebi',
            name: 'FineBI',
            icon: 'fas fa-chart-bar',
            description: 'FineBI商业智能分析平台',
            color: '#fdcb6e',
            count: 1100,
            type: 'bi'
        },
        {
            id: 'finedatalink',
            name: 'FineDataLink',
            icon: 'fas fa-link',
            description: 'FineDataLink数据集成平台',
            color: '#ff6b6b',
            count: 1,
            type: 'integration'
        },
        {
            id: 'jiandaoyun',
            name: '简道云',
            icon: 'fas fa-cloud',
            description: '简道云无代码应用搭建平台',
            color: '#00d4aa',
            count: 1,
            type: 'application'
        }
    ],

    // 数据资产分类
    assetCategories: [
        {
            id: 'database',
            name: '数据库资产',
            icon: 'fas fa-database',
            description: 'MySQL、PostgreSQL等关系型数据库资产',
            color: '#4285f4',
            count: 10400
        },
        {
            id: 'report',
            name: '报表资产',
            icon: 'fas fa-chart-line',
            description: 'FineReport报表、业务分析报告等可视化资产',
            color: '#fd79a8',
            count: 1400
        },
        {
            id: 'bi',
            name: 'BI分析资产',
            icon: 'fas fa-chart-bar',
            description: 'FineBI仪表板、数据分析模型等商业智能资产',
            color: '#fdcb6e',
            count: 1100
        },
        {
            id: 'integration',
            name: '数据集成资产',
            icon: 'fas fa-link',
            description: 'FineDataLink数据管道、ETL任务等集成资产',
            color: '#ff6b6b',
            count: 1
        },
        {
            id: 'application',
            name: '应用资产',
            icon: 'fas fa-cloud',
            description: '简道云应用、无代码平台等应用资产',
            color: '#00d4aa',
            count: 1
        },
        {
            id: 'api',
            name: 'API资产',
            icon: 'fas fa-plug',
            description: '各平台提供的API接口、数据服务等',
            color: '#55a3ff',
            count: 89
        }
    ],

    // 数据资产域配置
    domains: [
        {
            id: 'cust',
            name: '客户-cust',
            icon: 'fas fa-users',
            description: '客户基础信息、客户关系管理、客户画像分析等核心客户数据资产',
            assetCount: 98,
            apiCount: 15,
            reportCount: 28,
            updateFreq: '实时',
            color: '#8e44ad',
            subDomains: ['客户基础信息', '客户关系', '客户画像', '客户行为'],
            assetTypes: ['MySQL表', 'FineReport报表', 'FineBI仪表板', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        },
        {
            id: 'ts',
            name: '客户服务与技术支持-ts',
            icon: 'fas fa-headset',
            description: '客户服务、技术支持、问题跟踪、服务质量等客服技术数据资产',
            assetCount: 24,
            apiCount: 8,
            reportCount: 12,
            updateFreq: '实时',
            color: '#e91e63',
            subDomains: ['客户服务', '技术支持', '问题管理', '服务质量'],
            assetTypes: ['PostgreSQL表', 'FineReport服务报表', 'FineBI服务分析', 'API接口'],
            platforms: ['PostgreSQL', 'FineReport', 'FineBI']
        },
        {
            id: 'mkt',
            name: '市场-mkt',
            icon: 'fas fa-bullhorn',
            description: '市场活动、品牌推广、渠道管理、市场分析等市场营销数据资产',
            assetCount: 24,
            apiCount: 9,
            reportCount: 15,
            updateFreq: '每日',
            color: '#e91e63',
            subDomains: ['市场活动', '品牌推广', '渠道管理', '竞品分析'],
            assetTypes: ['MySQL表', 'FineReport营销报表', 'FineBI市场分析', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        },
        {
            id: 'hr',
            name: '人事-hr',
            icon: 'fas fa-user-tie',
            description: '人员组织架构、绩效考核、薪酬管理、人才发展等人力资源数据资产',
            assetCount: 44,
            apiCount: 8,
            reportCount: 18,
            updateFreq: '每日',
            color: '#00bcd4',
            subDomains: ['组织架构', '绩效管理', '薪酬福利', '人才发展'],
            assetTypes: ['MySQL表', 'FineReport人事报表', '简道云HR应用', 'API接口'],
            platforms: ['MySQL', 'FineReport', '简道云']
        },
        {
            id: 'idp',
            name: 'IDP',
            icon: 'fas fa-shield-alt',
            description: '身份认证、权限管理、安全策略等身份数据平台资产',
            assetCount: 0,
            apiCount: 0,
            reportCount: 0,
            updateFreq: '按需',
            color: '#795548',
            subDomains: ['身份认证', '权限管理', '安全策略', '访问控制'],
            assetTypes: ['安全配置', '权限表', 'API接口'],
            platforms: ['安全平台', 'API网关']
        },
        {
            id: 'oprn',
            name: '运营-oprn',
            icon: 'fas fa-cogs',
            description: '运营活动管理、营销分析、用户运营、产品运营等运营数据资产',
            assetCount: 112,
            apiCount: 22,
            reportCount: 35,
            updateFreq: '每小时',
            color: '#8e44ad',
            subDomains: ['营销运营', '用户运营', '产品运营', '数据运营'],
            assetTypes: ['PostgreSQL表', 'FineBI运营分析', '简道云应用', 'API接口'],
            platforms: ['PostgreSQL', 'FineBI', '简道云']
        },
        {
            id: 'jdy',
            name: '简道云持有业务-jdy',
            icon: 'fas fa-cloud',
            description: '简道云平台上的业务应用、流程管理、表单数据等业务资产',
            assetCount: 6,
            apiCount: 3,
            reportCount: 4,
            updateFreq: '实时',
            color: '#e91e63',
            subDomains: ['业务应用', '流程管理', '表单数据', '工作流'],
            assetTypes: ['简道云应用', '表单数据', '流程配置', 'API接口'],
            platforms: ['简道云']
        },
        {
            id: 'dw',
            name: '数仓治理-dw',
            icon: 'fas fa-warehouse',
            description: '数据仓库治理、数据质量管理、元数据管理等数据治理资产',
            assetCount: 1,
            apiCount: 1,
            reportCount: 1,
            updateFreq: '每日',
            color: '#e91e63',
            subDomains: ['数据质量', '元数据管理', '数据标准', '治理规则'],
            assetTypes: ['治理规则', '质量报告', '元数据', 'API接口'],
            platforms: ['数据治理平台', 'FineDataLink']
        },
        {
            id: 'pub',
            name: '公共数据-pub',
            icon: 'fas fa-database',
            description: '字典信息、地理信息、日期维度、财务科目等公共基础数据资产',
            assetCount: 14,
            apiCount: 5,
            reportCount: 8,
            updateFreq: '按需',
            color: '#4caf50',
            subDomains: ['字典维度', '地理信息', '时间维度', '基础配置'],
            assetTypes: ['MySQL维度表', 'PostgreSQL配置表', 'FineDataLink集成', 'API接口'],
            platforms: ['MySQL', 'PostgreSQL', 'FineDataLink']
        },
        {
            id: 'sales',
            name: '销售过程-sales',
            icon: 'fas fa-chart-line',
            description: '销售过程管理、商机跟踪、合同管理、项目实施等销售全流程数据资产',
            assetCount: 288,
            apiCount: 45,
            reportCount: 85,
            updateFreq: '每小时',
            color: '#ff5722',
            subDomains: ['商机管理', '合同管理', '项目实施', '销售分析'],
            assetTypes: ['MySQL表', 'PostgreSQL表', 'FineReport报表', 'FineBI分析'],
            platforms: ['MySQL', 'PostgreSQL', 'FineReport', 'FineBI']
        },
        {
            id: 'prod',
            name: '产品与研发-prod',
            icon: 'fas fa-code',
            description: '产品管理、研发项目、技术文档、版本管理等产品研发数据资产',
            assetCount: 50,
            apiCount: 12,
            reportCount: 20,
            updateFreq: '每日',
            color: '#4caf50',
            subDomains: ['产品管理', '研发项目', '技术文档', '版本管理'],
            assetTypes: ['MySQL表', 'PostgreSQL表', 'FineReport报表', 'API接口'],
            platforms: ['MySQL', 'PostgreSQL', 'FineReport']
        },
        {
            id: 'gd',
            name: '行政-gd',
            icon: 'fas fa-building',
            description: '行政管理、办公资源、会议管理、资产管理等行政数据资产',
            assetCount: 15,
            apiCount: 4,
            reportCount: 8,
            updateFreq: '每日',
            color: '#8e44ad',
            subDomains: ['行政管理', '办公资源', '会议管理', '资产管理'],
            assetTypes: ['MySQL表', '简道云应用', 'FineReport报表', 'API接口'],
            platforms: ['MySQL', '简道云', 'FineReport']
        },
        {
            id: 'fin',
            name: '财务-fin',
            icon: 'fas fa-coins',
            description: '财务核算、资金管理、成本控制、财务分析等财务管理数据资产',
            assetCount: 124,
            apiCount: 18,
            reportCount: 45,
            updateFreq: '每日',
            color: '#4caf50',
            subDomains: ['财务核算', '资金管理', '成本分析', '预算管理'],
            assetTypes: ['MySQL表', 'FineReport财务报表', 'FineBI财务分析', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        }
    ],

    // 热门数据资产
    popularAssets: [
        {
            name: 'cust_company_info',
            domain: '客户-cust',
            type: 'MySQL表',
            platform: 'MySQL',
            description: '客户公司基础信息表，存储企业客户的核心信息',
            recordCount: '125,847',
            lastUpdate: '2小时前',
            usage: 'high',
            icon: 'fas fa-database'
        },
        {
            name: 'sales_performance_dashboard',
            domain: '销售过程-sales',
            type: 'FineBI仪表板',
            platform: 'FineBI',
            description: '销售业绩实时监控仪表板，展示关键销售指标',
            recordCount: '实时',
            lastUpdate: '实时',
            usage: 'high',
            icon: 'fas fa-chart-bar'
        },
        {
            name: 'financial_monthly_report',
            domain: '财务-fin',
            type: 'FineReport报表',
            platform: 'FineReport',
            description: '财务月度分析报表，包含收支、成本等核心财务指标',
            recordCount: '月度',
            lastUpdate: '1天前',
            usage: 'medium',
            icon: 'fas fa-chart-line'
        },
        {
            name: 'customer_service_data',
            domain: '客户服务与技术支持-ts',
            type: 'PostgreSQL表',
            platform: 'PostgreSQL',
            description: '客户服务工单数据表，记录所有客服处理记录',
            recordCount: '45,678',
            lastUpdate: '30分钟前',
            usage: 'high',
            icon: 'fas fa-server'
        },
        {
            name: 'hr_management_app',
            domain: '人事-hr',
            type: '简道云应用',
            platform: '简道云',
            description: '人力资源管理应用，包含员工信息、考勤、绩效等功能',
            recordCount: '2,345',
            lastUpdate: '4小时前',
            usage: 'medium',
            icon: 'fas fa-cloud'
        },
        {
            name: 'data_integration_pipeline',
            domain: '公共数据-pub',
            type: 'FineDataLink管道',
            platform: 'FineDataLink',
            description: '核心数据集成管道，负责多源数据的ETL处理',
            recordCount: '管道',
            lastUpdate: '1小时前',
            usage: 'high',
            icon: 'fas fa-link'
        },
        {
            name: 'marketing_analysis_bi',
            domain: '市场-mkt',
            type: 'FineBI分析',
            platform: 'FineBI',
            description: '市场营销效果分析，包含渠道转化、ROI等关键指标',
            recordCount: '分析模型',
            lastUpdate: '6小时前',
            usage: 'medium',
            icon: 'fas fa-chart-bar'
        },
        {
            name: 'operation_data_mysql',
            domain: '运营-oprn',
            type: 'MySQL表',
            platform: 'MySQL',
            description: '运营活动数据表，记录用户行为和运营效果',
            recordCount: '892,456',
            lastUpdate: '1小时前',
            usage: 'high',
            icon: 'fas fa-database'
        },
        {
            name: 'product_development_data',
            domain: '产品与研发-prod',
            type: 'PostgreSQL表',
            platform: 'PostgreSQL',
            description: '产品研发项目数据表，记录产品开发进度和需求',
            recordCount: '15,234',
            lastUpdate: '3小时前',
            usage: 'medium',
            icon: 'fas fa-server'
        },
        {
            name: 'jiandaoyun_business_app',
            domain: '简道云持有业务-jdy',
            type: '简道云应用',
            platform: '简道云',
            description: '简道云业务流程应用，管理各类业务审批流程',
            recordCount: '1,567',
            lastUpdate: '2小时前',
            usage: 'medium',
            icon: 'fas fa-cloud'
        },
        {
            name: 'admin_office_management',
            domain: '行政-gd',
            type: '简道云应用',
            platform: '简道云',
            description: '行政办公管理应用，包含会议室预订、资产管理等',
            recordCount: '3,456',
            lastUpdate: '5小时前',
            usage: 'low',
            icon: 'fas fa-cloud'
        }
    ],

    // 数据血缘关系配置
    dataLineage: {
        nodes: [
            // 数据源层
            { id: 'mysql', name: 'MySQL\n业务数据库', x: 80, y: 120, type: 'mysql', icon: 'fas fa-database' },
            { id: 'postgresql', name: 'PostgreSQL\n分析数据库', x: 80, y: 220, type: 'postgresql', icon: 'fas fa-server' },
            { id: 'jiandaoyun', name: '简道云\n业务应用', x: 80, y: 320, type: 'jiandaoyun', icon: 'fas fa-cloud' },

            // 数据集成层
            { id: 'finedatalink', name: 'FineDataLink\n数据集成平台', x: 280, y: 220, type: 'finedatalink', icon: 'fas fa-link' },

            // 数据仓库层
            { id: 'datawarehouse', name: '数据仓库\nODS/DWD/DWS', x: 480, y: 180, type: 'warehouse', icon: 'fas fa-warehouse' },
            { id: 'datamart', name: '数据集市\nADS层', x: 480, y: 280, type: 'datamart', icon: 'fas fa-cubes' },

            // 应用层
            { id: 'finereport', name: 'FineReport\n报表平台', x: 680, y: 120, type: 'finereport', icon: 'fas fa-chart-line' },
            { id: 'finebi', name: 'FineBI\n商业智能', x: 680, y: 220, type: 'finebi', icon: 'fas fa-chart-bar' },
            { id: 'api_services', name: 'API服务\n数据接口', x: 680, y: 320, type: 'api', icon: 'fas fa-plug' },

            // 终端用户层
            { id: 'business_users', name: '业务用户\n决策分析', x: 880, y: 180, type: 'users', icon: 'fas fa-users' },
            { id: 'external_apps', name: '外部应用\n系统集成', x: 880, y: 280, type: 'external', icon: 'fas fa-external-link-alt' }
        ],
        links: [
            // 数据源到集成层
            { source: 'mysql', target: 'finedatalink', label: '业务数据抽取', color: '#4285f4' },
            { source: 'postgresql', target: 'finedatalink', label: '分析数据同步', color: '#336791' },
            { source: 'jiandaoyun', target: 'finedatalink', label: '应用数据导入', color: '#00d4aa' },

            // 集成层到数据仓库
            { source: 'finedatalink', target: 'datawarehouse', label: 'ETL处理', color: '#ff6b6b' },
            { source: 'datawarehouse', target: 'datamart', label: '数据建模', color: '#a29bfe' },

            // 数据仓库到应用层
            { source: 'datawarehouse', target: 'finereport', label: '报表数据源', color: '#fd79a8' },
            { source: 'datamart', target: 'finebi', label: 'BI分析数据', color: '#fdcb6e' },
            { source: 'datamart', target: 'api_services', label: 'API数据服务', color: '#55a3ff' },

            // 直连数据源到应用
            { source: 'mysql', target: 'finereport', label: '实时报表', color: '#74b9ff' },
            { source: 'postgresql', target: 'finebi', label: '直连分析', color: '#81ecec' },

            // 应用层到用户层
            { source: 'finereport', target: 'business_users', label: '报表展示', color: '#fd79a8' },
            { source: 'finebi', target: 'business_users', label: 'BI仪表板', color: '#fdcb6e' },
            { source: 'api_services', target: 'external_apps', label: '数据接口', color: '#55a3ff' },

            // 简道云直接服务用户
            { source: 'jiandaoyun', target: 'business_users', label: '业务流程', color: '#00d4aa' }
        ]
    },

    // 数据质量指标
    qualityMetrics: {
        completeness: 95,
        timeliness: 88,
        accuracy: 92,
        consistency: 90
    },

    // 统计信息
    statistics: {
        totalAssets: 800, // 根据截图中各域资产数量总和：98+24+24+44+0+112+6+1+14+288+50+15+124 = 800
        totalDomains: 13,
        totalApis: 150, // 根据各域API数量估算
        totalReports: 283, // 根据各域报表数量估算
        totalRecords: '15,678,234',
        dailyUpdates: '456,789'
    }
};

// 搜索配置
const SEARCH_CONFIG = {
    // 搜索建议
    suggestions: [
        'MySQL',
        'PostgreSQL',
        'FineReport',
        'FineBI',
        'FineDataLink',
        '简道云',
        '客户基础信息',
        '销售业绩',
        '财务报表',
        '运营分析',
        '人事管理',
        '技术支持',
        '市场营销',
        '数据集成'
    ],

    // 搜索结果类型
    resultTypes: {
        'MySQL表': 'MySQL数据表',
        'PostgreSQL表': 'PostgreSQL数据表',
        'FineReport报表': 'FineReport报表',
        'FineBI仪表板': 'FineBI仪表板',
        'FineBI分析': 'FineBI分析',
        'FineDataLink管道': 'FineDataLink数据管道',
        '简道云应用': '简道云应用',
        'API接口': 'API接口',
        'domain': '资产域'
    }
};

// 主题配置
const THEME_CONFIG = {
    colors: {
        primary: '#4285f4',
        secondary: '#fd79a8',
        accent: '#26de81',
        background: '#f8f9fa',
        surface: '#ffffff',
        text: '#212529',
        textSecondary: 'rgba(108, 117, 125, 0.8)',
        // 平台色彩
        mysql: '#4285f4',
        postgresql: '#336791',
        finereport: '#fd79a8',
        finebi: '#fdcb6e',
        finedatalink: '#ff6b6b',
        jiandaoyun: '#00d4aa'
    },

    animations: {
        duration: {
            fast: '0.2s',
            normal: '0.3s',
            slow: '0.5s'
        },
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DATA_CONFIG,
        SEARCH_CONFIG,
        THEME_CONFIG
    };
}
